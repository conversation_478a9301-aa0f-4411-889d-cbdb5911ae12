{"name": "itrip-backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch --debug", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "start:staging": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "import-locations": "ts-node scripts/import-locations.ts", "monitor-cache": "node scripts/monitor-cache.js"}, "dependencies": {"@dqbd/tiktoken": "^1.0.21", "@instructor-ai/instructor": "^1.6.3", "@nestjs/axios": "^4.0.0", "@nestjs/common": "^11.0.9", "@nestjs/config": "^4.0.0", "@nestjs/core": "^11.0.9", "@nestjs/jwt": "^11.0.0", "@nestjs/mongoose": "^11.0.3", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.9", "@nestjs/platform-socket.io": "^11.1.2", "@nestjs/schedule": "^6.0.0", "@nestjs/serve-static": "^5.0.3", "@nestjs/swagger": "^11.0.3", "@nestjs/terminus": "^11.0.0", "@nestjs/throttler": "^6.4.0", "@nestjs/websockets": "^11.0.9", "@types/pdfkit": "^0.17.2", "axios": "^1.7.9", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "countries-list": "^3.1.1", "csv-parse": "^5.6.0", "google-auth-library": "^9.15.1", "gpt-tokenizer": "^2.8.1", "helmet": "^8.1.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "jwks-rsa": "^3.2.0", "mongoose": "^8.15.1", "node-jose": "^2.2.0", "openai": "^4.85.1", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pdfkit": "^0.17.1", "reflect-metadata": "^0.2.2", "resend": "^4.6.0", "rxjs": "^7.8.1", "socket.io": "^4.8.1", "swagger-ui-express": "^5.0.1", "zod": "^3.24.1"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.2", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/bun": "latest", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/node": "^22.13.1", "@types/passport-google-oauth20": "^2.0.16", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/supertest": "^6.0.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^15.15.0", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node", "setupFilesAfterEnv": ["<rootDir>/../test/setup.ts"], "clearMocks": true, "resetMocks": true, "restoreMocks": true}}