import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import * as jwt from 'jsonwebtoken';
import { Model } from 'mongoose';
import { DaysBalanceService } from '../../days-balance/days-balance.service';
import { PaymentEmailService } from '../../common/payment-email.service';
import {
  AppleNotificationPayloadDto,
  AppleNotificationType,
  AppleRenewalInfo,
  AppleTransactionInfo,
  AppleWebhookDto
} from '../dto/apple-webhook.dto';
import { PaymentPlatform, PaymentReceipt } from '../schemas/payment-receipt.schema';
import { WebhookVerificationService } from './webhook-verification.service';
import { ProductConfigService } from './product-config.service';

@Injectable()
export class AppleWebhookService {
  private readonly logger = new Logger(AppleWebhookService.name);

  constructor(
    private readonly daysBalanceService: DaysBalanceService,
    private readonly webhookVerificationService: WebhookVerificationService,
    private readonly paymentEmailService: PaymentEmailService,
    @InjectModel(PaymentReceipt.name)
    private paymentReceiptModel: Model<PaymentReceipt>,
    private readonly productConfigService: ProductConfigService,
  ) { }

  /**
   * Process Apple App Store Server Notification
   */
  async processWebhook(webhookDto: AppleWebhookDto): Promise<{ success: boolean; message: string }> {
    try {
      this.logger.log('Processing Apple webhook notification');

      // Verify and decode the signed payload
      const payload = await this.verifyAndDecodePayload(webhookDto.signedPayload);

      this.logger.log(`Received Apple notification: ${payload.notificationType}`);

      // Process based on notification type
      await this.handleNotification(payload);

      return {
        success: true,
        message: 'Webhook processed successfully'
      };
    } catch (error) {
      this.logger.error(`Error processing Apple webhook: ${error.message}`, error.stack);
      throw new BadRequestException(`Webhook processing failed: ${error.message}`);
    }
  }

  /**
   * Verify JWT signature and decode payload
   */
  private async verifyAndDecodePayload(signedPayload: string): Promise<AppleNotificationPayloadDto> {
    try {
      // Use proper signature verification
      const verificationResult = await this.webhookVerificationService.verifyAppleWebhook(signedPayload);

      if (!verificationResult.isValid) {
        throw new Error(`Webhook verification failed: ${verificationResult.error}`);
      }

      return verificationResult.payload as AppleNotificationPayloadDto;
    } catch (error) {
      this.logger.error(`JWT verification failed: ${error.message}`);
      throw new Error('Invalid webhook signature');
    }
  }

  /**
   * Handle different notification types
   */
  private async handleNotification(payload: AppleNotificationPayloadDto): Promise<void> {
    const { notificationType, data } = payload;

    switch (notificationType) {
      case AppleNotificationType.SUBSCRIBED:
        await this.handleSubscribed(data);
        break;

      case AppleNotificationType.DID_RENEW:
        await this.handleRenewal(data);
        break;

      case AppleNotificationType.DID_FAIL_TO_RENEW:
        await this.handleRenewalFailure(data);
        break;

      case AppleNotificationType.EXPIRED:
        await this.handleExpiration(data);
        break;

      case AppleNotificationType.DID_CHANGE_RENEWAL_STATUS:
        await this.handleRenewalStatusChange(data);
        break;

      case AppleNotificationType.REFUND:
        await this.handleRefund(data);
        break;

      case AppleNotificationType.REVOKE:
        await this.handleRevocation(data);
        break;

      case AppleNotificationType.GRACE_PERIOD_EXPIRED:
        await this.handleGracePeriodExpired(data);
        break;

      case AppleNotificationType.TEST:
        this.logger.log('Received test notification from Apple');
        break;

      default:
        this.logger.warn(`Unhandled Apple notification type: ${notificationType}`);
    }
  }

  /**
   * Handle new subscription
   */
  private async handleSubscribed(data: any): Promise<void> {
    const transactionInfo = this.extractTransactionInfo(data);
    const userId = await this.findUserByTransactionId(transactionInfo.originalTransactionId);

    if (!userId) {
      this.logger.warn(`User not found for transaction: ${transactionInfo.originalTransactionId}`);
      return;
    }

    this.logger.log(`Processing subscription for user ${userId}`);

    // Update subscription status
    const planId = this.mapProductIdToPlan(transactionInfo.productId);
    if (planId) {
      await this.daysBalanceService.updateSubscriptionPlan(userId, planId as any);
    }

    // Send purchase completion email
    try {
      const receipt = await this.paymentReceiptModel.findOne({
        originalTransactionId: transactionInfo.originalTransactionId,
        platform: PaymentPlatform.IOS
      });

      if (receipt) {
        // Calculate next billing date (30 days from now for subscriptions)
        const nextBillingDate = new Date();
        nextBillingDate.setDate(nextBillingDate.getDate() + 30);

        await this.paymentEmailService.sendPurchaseCompletionEmail(
          userId,
          (receipt as any)._id.toString(),
          {
            nextBillingDate: this.formatDate(nextBillingDate)
          }
        );
      }
    } catch (error) {
      this.logger.error(`Error sending purchase completion email: ${error.message}`, error.stack);
      // Don't throw - email failure shouldn't break webhook processing
    }
  }

  /**
   * Handle subscription renewal
   */
  private async handleRenewal(data: any): Promise<void> {
    const transactionInfo = this.extractTransactionInfo(data);
    const userId = await this.findUserByTransactionId(transactionInfo.originalTransactionId);

    if (!userId) {
      this.logger.warn(`User not found for renewal: ${transactionInfo.originalTransactionId}`);
      return;
    }

    this.logger.log(`Processing renewal for user ${userId}`);

    // Reset subscription days
    await this.daysBalanceService.resetSubscriptionDays(userId);

    // Send renewal success email
    try {
      const receipt = await this.paymentReceiptModel.findOne({
        originalTransactionId: transactionInfo.originalTransactionId,
        platform: PaymentPlatform.IOS
      });

      if (receipt) {
        // Calculate next billing date (30 days from now)
        const nextBillingDate = new Date();
        nextBillingDate.setDate(nextBillingDate.getDate() + 30);

        await this.paymentEmailService.sendRenewalSuccessEmail(
          userId,
          (receipt as any)._id.toString(),
          {
            nextBillingDate: this.formatDate(nextBillingDate)
          }
        );
      }
    } catch (error) {
      this.logger.error(`Error sending renewal success email: ${error.message}`, error.stack);
      // Don't throw - email failure shouldn't break webhook processing
    }
  }

  /**
   * Handle renewal failure
   */
  private async handleRenewalFailure(data: any): Promise<void> {
    const transactionInfo = this.extractTransactionInfo(data);
    const userId = await this.findUserByTransactionId(transactionInfo.originalTransactionId);

    if (!userId) {
      return;
    }

    this.logger.log(`Processing renewal failure for user ${userId}`);

    // Send renewal failure email
    try {
      const planName = this.getPlanNameFromProductId(transactionInfo.productId);

      // Calculate retry date (3 days from now)
      const retryDate = new Date();
      retryDate.setDate(retryDate.getDate() + 3);

      // Calculate grace period end (7 days from now)
      const gracePeriodEnd = new Date();
      gracePeriodEnd.setDate(gracePeriodEnd.getDate() + 7);

      await this.paymentEmailService.sendRenewalFailureEmail(userId, {
        planName,
        amount: 0, // We don't have the amount in the webhook data
        failureDate: this.formatDate(new Date()),
        retryDate: this.formatDate(retryDate),
        gracePeriodEnd: this.formatDate(gracePeriodEnd),
        reason: 'Payment method declined'
      });
    } catch (error) {
      this.logger.error(`Error sending renewal failure email: ${error.message}`, error.stack);
      // Don't throw - email failure shouldn't break webhook processing
    }

  }

  /**
   * Handle subscription expiration
   */
  private async handleExpiration(data: any): Promise<void> {
    const transactionInfo = this.extractTransactionInfo(data);
    const userId = await this.findUserByTransactionId(transactionInfo.originalTransactionId);

    if (!userId) {
      return;
    }

    this.logger.log(`Processing expiration for user ${userId}`);

    // Downgrade to free plan
    await this.daysBalanceService.updateSubscriptionPlan(userId, 'free' as any);
  }

  /**
   * Handle refund
   */
  private async handleRefund(data: any): Promise<void> {
    const transactionInfo = this.extractTransactionInfo(data);
    const userId = await this.findUserByTransactionId(transactionInfo.originalTransactionId);

    if (!userId) {
      return;
    }

    this.logger.log(`Processing refund for user ${userId}`);

    // Handle refund logic - might need to deduct days or downgrade plan
    this.daysBalanceService.updateSubscriptionPlan(userId, 'free' as any);
  }

  /**
   * Handle revocation
   */
  private async handleRevocation(data: any): Promise<void> {
    const transactionInfo = this.extractTransactionInfo(data);
    const userId = await this.findUserByTransactionId(transactionInfo.originalTransactionId);

    if (!userId) {
      return;
    }

    this.logger.log(`Processing revocation for user ${userId}`);

    // Handle subscription revocation
    await this.daysBalanceService.updateSubscriptionPlan(userId, 'free' as any);
  }

  /**
   * Handle renewal status change
   */
  private async handleRenewalStatusChange(data: any): Promise<void> {
    const renewalInfo = this.extractRenewalInfo(data);
    this.logger.log(`Renewal status changed to: ${renewalInfo.autoRenewStatus}`);

    // Update renewal preferences in your system if needed
  }

  /**
   * Handle grace period expiration
   */
  private async handleGracePeriodExpired(data: any): Promise<void> {
    const transactionInfo = this.extractTransactionInfo(data);
    const userId = await this.findUserByTransactionId(transactionInfo.originalTransactionId);

    if (!userId) {
      return;
    }

    this.logger.log(`Grace period expired for user ${userId}`);

    // Downgrade to free plan after grace period
    await this.daysBalanceService.updateSubscriptionPlan(userId, 'free' as any);

    // Send grace period expired email
    try {
      const planName = this.getPlanNameFromProductId(transactionInfo.productId);

      await this.paymentEmailService.sendPaymentIssuesEmail(userId, {
        planName,
        issueType: 'grace_period_expired',
        issueDate: this.formatDate(new Date()),
        nextAction: 'Update your payment method in the app settings to resubscribe and restore your full plan benefits.',
        supportMessage: 'You can still use Trip Itinerary Planner with 5 days per month on the free plan.'
      });
    } catch (error) {
      this.logger.error(`Error sending grace period expired email: ${error.message}`, error.stack);
      // Don't throw - email failure shouldn't break webhook processing
    }
  }

  /**
   * Extract transaction info from webhook data
   */
  private extractTransactionInfo(data: any): AppleTransactionInfo {
    // The actual structure depends on Apple's webhook format
    // This is a simplified version
    return data.signedTransactionInfo ?
      jwt.decode(data.signedTransactionInfo) as AppleTransactionInfo :
      data.transactionInfo;
  }

  /**
   * Extract renewal info from webhook data
   */
  private extractRenewalInfo(data: any): AppleRenewalInfo {
    return data.signedRenewalInfo ?
      jwt.decode(data.signedRenewalInfo) as AppleRenewalInfo :
      data.renewalInfo;
  }

  /**
   * Find user by original transaction ID
   */
  private async findUserByTransactionId(originalTransactionId: string): Promise<string | null> {
    const receipt = await this.paymentReceiptModel.findOne({
      originalTransactionId,
      platform: PaymentPlatform.IOS
    });

    return receipt ? receipt.userId : null;
  }

  /**
   * Map Apple product ID to internal plan ID
   */
  private mapProductIdToPlan(productId: string): string | null {
    const product = this.productConfigService.getProductByStoreId(productId, 'ios');
    return product ? product.id : null;
  }

  /**
   * Get plan name from product ID
   */
  private getPlanNameFromProductId(productId: string): string {
    if (productId.includes('free')) return 'Free';
    if (productId.includes('pro')) return 'Pro';
    if (productId.includes('premium')) return 'Premium';
    return 'Unknown Plan';
  }

  /**
   * Format date for display
   */
  private formatDate(date: Date): string {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZoneName: 'short'
    }).format(new Date(date));
  }
}
